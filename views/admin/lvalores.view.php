<?php
#region region DOCS

#endregion DOCS
?>
<!DOCTYPE html>
<html lang="es" class="dark-mode">
<head>
	<meta charset="utf-8"/>
	<title>Gestión de Valores | Admin</title>
	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
	<meta content="" name="description"/>
	<meta content="" name="author"/>
	
	<?php #region HEAD ?>
	<?php require_once __ROOT__ . '/views/admin/head.view.php'; ?>
	<link href="<?php echo RUTA_RESOURCES ?>css/dashboard.css" rel="stylesheet"/>
	<?php #endregion HEAD ?>
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
	<span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed ">
	<!-- #header -->
	<?php require_once __ROOT__ . '/views/admin/header.view.php'; ?>
	
	<!-- #sidebar -->
	<?php require_once __ROOT__ . '/views/admin/sidebar.view.php'; ?>
	<!-- END #sidebar -->
	
	<!-- BEGIN #content -->
	<div id="content" class="app-content">
		
		<!-- BEGIN page-header -->
		<h1 class="page-header">Gestión de Valores <small>Administrar valores corporativos</small></h1>
		<!-- END page-header -->
		
		<?php #region region VALORES FORM ?>
		<div class="panel panel-inverse no-border-radious">
			<div class="panel-heading no-border-radious">
				<h4 class="panel-title">Gestión de Valores</h4>
				<div class="panel-heading-btn">
					<a href="javascript:;" class="btn btn-xs btn-icon btn-default" data-toggle="panel-expand"><i class="fa fa-expand"></i></a>
					<a href="javascript:;" class="btn btn-xs btn-icon btn-warning" data-toggle="panel-collapse"><i class="fa fa-minus"></i></a>
				</div>
			</div>
			<div class="panel-body">
				<div class="row mb-3">
					<div class="col-12 text-end">
						<button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#modalValor" onclick="abrirModalCrear()">
							<i class="fa fa-plus fa-fw me-1"></i> Crear Nuevo Valor
						</button>
					</div>
				</div>
				
				<div class="table-responsive">
					<table class="table table-bordered align-middle">
						<thead>
						<tr>
							<th width="120" class="text-center">Acciones</th>
							<th width="150" class="text-center">Imagen</th>
							<th>Título</th>
							<th>Descripción</th>
							<th width="100" class="text-center">Prioridad</th>
						</tr>
						</thead>
						<tbody>
						<?php if (!empty($valores)): ?>
							<?php foreach ($valores as $valor): ?>
								<tr>
									<td class="text-center">
										<button type="button" class="btn btn-xs btn-warning me-1"
										        onclick="abrirModalEditar(<?php echo $valor->getId(); ?>)"
										        title="Editar valor">
											<i class="fa fa-edit"></i>
										</button>
										<button type="button" class="btn btn-xs btn-danger"
										        onclick="eliminarValor(<?php echo $valor->getId(); ?>)"
										        title="Eliminar valor">
											<i class="fa fa-trash"></i>
										</button>
									</td>
									<td class="text-center">
										<?php if ($valor->getImagen()): ?>
											<img src="<?php echo RUTA_RESOURCES ?>images/valores/<?php echo htmlspecialchars($valor->getImagen()); ?>"
											     alt="Valor" class="valor-thumbnail" style="max-width: 100px; max-height: 50px; object-fit: contain;">
										<?php else: ?>
											<span class="text-muted">Sin imagen</span>
										<?php endif; ?>
									</td>
									<td>
										<strong><?php echo htmlspecialchars($valor->getTitulo()); ?></strong>
									</td>
									<td>
										<div style="max-width: 300px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
											<?php echo htmlspecialchars($valor->getTexto()); ?>
										</div>
									</td>
									<td class="text-center">
										<span class="badge bg-primary"><?php echo $valor->getPrioridad(); ?></span>
									</td>
								</tr>
							<?php endforeach; ?>
						<?php else: ?>
							<tr>
								<td colspan="5" class="text-center text-muted">
									<i class="fa fa-info-circle me-2"></i>No hay valores registrados
								</td>
							</tr>
						<?php endif; ?>
						</tbody>
					</table>
				</div>
			</div>
		</div>
		<?php #endregion VALORES FORM ?>
	</div>
	<!-- END #content -->
	
	<!-- BEGIN scroll-top-btn -->
	<a href="javascript:;" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
	<!-- END scroll-top-btn -->
</div>
<!-- END #app -->

<?php #region MODAL VALOR ?>
<!-- Modal for Create/Edit Valor -->
<div class="modal fade" id="modalValor" tabindex="-1" aria-labelledby="modalValorLabel" aria-hidden="true">
	<div class="modal-dialog modal-lg">
		<div class="modal-content">
			<div class="modal-header">
				<h5 class="modal-title" id="modalValorLabel">Crear Nuevo Valor</h5>
				<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
			</div>
			<form id="formValor" novalidate enctype="multipart/form-data">
				<div class="modal-body">
					<input type="hidden" id="valorId" name="id" value="">
					<input type="hidden" id="action" name="action" value="crear">
					
					<!-- Image Upload Field -->
					<div class="mb-3">
						<label for="imagen" class="form-label">Imagen <span class="text-danger">*</span></label>
						<input type="file" class="form-control" id="imagen" name="imagen" accept=".jpg,.jpeg,.png,.webp">
						<div class="form-text">
							<strong>Nota:</strong> La imagen debe tener dimensiones de 200 x 200 píxeles.
							                       Formatos permitidos: JPG, PNG, WebP. Tamaño máximo: 8MB.
						</div>
						<div class="invalid-feedback" id="imagen-error"></div>
						
						<!-- Current Image Preview (for edit mode) -->
						<div id="currentImagePreview" class="mt-2" style="display: none;">
							<label class="form-label">Imagen actual:</label><br>
							<img id="currentImage" src="" alt="Imagen actual" style="max-width: 150px; max-height: 150px; object-fit: contain; border: 1px solid #ddd; padding: 5px;">
						</div>
						
						<!-- New Image Preview -->
						<div id="newImagePreview" class="mt-2" style="display: none;">
							<label class="form-label">Vista previa:</label><br>
							<img id="previewImage" src="" alt="Vista previa" style="max-width: 150px; max-height: 150px; object-fit: contain; border: 1px solid #ddd; padding: 5px;">
						</div>
					</div>
					
					<!-- Title Field -->
					<div class="mb-3">
						<label for="titulo" class="form-label">Título <span class="text-danger">*</span></label>
						<input type="text" class="form-control" id="titulo" name="titulo" maxlength="100" required>
						<div class="invalid-feedback" id="titulo-error"></div>
					</div>
					
					<!-- Description Field -->
					<div class="mb-3">
						<label for="descripcion" class="form-label">Descripción <span class="text-danger">*</span></label>
						<textarea class="form-control" id="descripcion" name="descripcion" rows="7" maxlength="1000" required></textarea>
						<div class="form-text">
							<span id="descripcion-count">0</span>/1000 caracteres
						</div>
						<div class="invalid-feedback" id="descripcion-error"></div>
					</div>
					
					<!-- Priority Field -->
					<div class="mb-3">
						<label for="prioridad" class="form-label">Prioridad <span class="text-danger">*</span></label>
						<input type="number" class="form-control" id="prioridad" name="prioridad" min="1" max="999" required>
						<div class="form-text">
							Número del 1 al 999. Los valores se mostrarán ordenados por prioridad (menor a mayor).
						</div>
						<div class="invalid-feedback" id="prioridad-error"></div>
					</div>
					
					<div class="alert alert-info">
						<i class="fa fa-info-circle"></i>
						<strong>Nota:</strong> Los valores se mostrarán en el sitio web ordenados por prioridad (menor a mayor).
					</div>
				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
					<button type="submit" class="btn btn-primary" id="btnSubmit">
						<span class="spinner-border spinner-border-sm me-1 d-none" id="loadingSpinner"></span>
						Crear
					</button>
				</div>
			</form>
		</div>
	</div>
</div>
<?php #endregion MODAL VALOR ?>

<?php #region region JS ?>
<?php require_once __ROOT__ . '/views/admin/core_js.view.php'; ?>

<script src="<?php echo RUTA_RESOURCES ?>js/lvalores.js"></script>

<?php #endregion JS ?>
</body>
</html>
