/**
 * VALORES ADMIN MODULE JAVASCRIPT
 * Handles CRUD operations for valores management
 */

$(document).ready(function() {
    // Initialize form handlers
    initializeFormHandlers();

    // Initialize modal handlers
    initializeModalHandlers();
    
    // Initialize character counter
    initializeCharacterCounter();
});

/**
 * Initialize form submission handlers
 */
function initializeFormHandlers() {
    $('#formValor').on('submit', function(e) {
        e.preventDefault();
        
        // Clear previous validation states
        clearValidationStates();
        
        // Validate form
        if (!validateForm()) {
            return;
        }
        
        // Show loading state
        showLoadingState(true);
        
        // Prepare form data
        const formData = new FormData(this);
        
        // Submit form via AJAX
        $.ajax({
            url: 'listado-valores',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            dataType: 'json',
            success: function(response) {
                handleFormSuccess(response);
            },
            error: function(xhr, status, error) {
                handleFormError(xhr, status, error);
            },
            complete: function() {
                showLoadingState(false);
            }
        });
    });
}

/**
 * Initialize modal event handlers
 */
function initializeModalHandlers() {
    // Reset form when modal is hidden
    $('#modalValor').on('hidden.bs.modal', function() {
        resetForm();
    });
    
    // Handle image preview
    $('#imagen').on('change', function() {
        previewImage(this);
    });
}

/**
 * Initialize character counter for description
 */
function initializeCharacterCounter() {
    $('#descripcion').on('input', function() {
        const currentLength = $(this).val().length;
        const maxLength = 1000;
        $('#descripcion-count').text(currentLength);
        
        if (currentLength > maxLength * 0.9) {
            $('#descripcion-count').addClass('text-warning');
        } else {
            $('#descripcion-count').removeClass('text-warning');
        }
        
        if (currentLength >= maxLength) {
            $('#descripcion-count').addClass('text-danger').removeClass('text-warning');
        } else {
            $('#descripcion-count').removeClass('text-danger');
        }
    });
}

/**
 * Open modal for creating new valor
 */
function abrirModalCrear() {
    resetForm();
    $('#modalValorLabel').text('Crear Nuevo Valor');
    $('#action').val('crear');
    $('#btnSubmit').text('Crear');
    $('#imagen').prop('required', true);
    $('#currentImagePreview').hide();
}

/**
 * Open modal for editing existing valor
 */
function abrirModalEditar(valorId) {
    resetForm();
    $('#modalValorLabel').text('Editar Valor');
    $('#action').val('editar');
    $('#valorId').val(valorId);
    $('#btnSubmit').text('Actualizar');
    $('#imagen').prop('required', false);
    
    // Show loading state
    showLoadingState(true);
    
    $.ajax({
        url: 'listado-valores',
        type: 'POST',
        data: {
            action: 'obtener',
            id: valorId
        },
        dataType: 'json',
        success: function(response) {
            if (response.success && response.valor) {
                const valor = response.valor;
                
                // Populate form fields
                $('#titulo').val(valor.titulo);
                $('#descripcion').val(valor.descripcion);
                $('#prioridad').val(valor.prioridad);
                
                // Update character counter
                $('#descripcion-count').text(valor.descripcion ? valor.descripcion.length : 0);
                
                // Show current image if exists
                if (valor.imagen) {
                    $('#currentImage').attr('src', RUTA_RESOURCES + 'images/valores/' + valor.imagen);
                    $('#currentImagePreview').show();
                }
                
                // Show modal
                $('#modalValor').modal('show');
            } else {
                showSweetAlertError('Error', response.message || 'No se pudo cargar los datos del valor.');
            }
        },
        error: function(xhr, status, error) {
            showSweetAlertError('Error', 'Error al cargar los datos del valor.');
        },
        complete: function() {
            showLoadingState(false);
        }
    });
}

/**
 * Delete valor with confirmation
 */
function eliminarValor(valorId) {
    Swal.fire({
        title: '¿Estás seguro?',
        text: 'Esta acción eliminará el valor permanentemente.',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'Sí, eliminar',
        cancelButtonText: 'Cancelar',
        background: '#2d353c',
        color: '#fff'
    }).then((result) => {
        if (result.isConfirmed) {
            // Show loading
            Swal.fire({
                title: 'Eliminando...',
                text: 'Por favor espere',
                allowOutsideClick: false,
                background: '#2d353c',
                color: '#fff',
                didOpen: () => {
                    Swal.showLoading();
                }
            });
            
            $.ajax({
                url: 'listado-valores',
                type: 'POST',
                data: {
                    action: 'eliminar',
                    id: valorId
                },
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        showSweetAlertSuccess('¡Eliminado!', response.message);
                        // Reload page to refresh the table
                        setTimeout(() => {
                            location.reload();
                        }, 1500);
                    } else {
                        showSweetAlertError('Error', response.message);
                    }
                },
                error: function(xhr, status, error) {
                    showSweetAlertError('Error', 'Error al eliminar el valor.');
                }
            });
        }
    });
}

/**
 * Validate form before submission
 */
function validateForm() {
    let isValid = true;
    
    // Validate title
    const titulo = $('#titulo').val().trim();
    if (!titulo) {
        showFieldError('titulo', 'El título es requerido.');
        isValid = false;
    } else if (titulo.length < 2) {
        showFieldError('titulo', 'El título debe tener al menos 2 caracteres.');
        isValid = false;
    } else if (titulo.length > 100) {
        showFieldError('titulo', 'El título no puede exceder 100 caracteres.');
        isValid = false;
    }
    
    // Validate description
    const descripcion = $('#descripcion').val().trim();
    if (!descripcion) {
        showFieldError('descripcion', 'La descripción es requerida.');
        isValid = false;
    } else if (descripcion.length < 10) {
        showFieldError('descripcion', 'La descripción debe tener al menos 10 caracteres.');
        isValid = false;
    } else if (descripcion.length > 1000) {
        showFieldError('descripcion', 'La descripción no puede exceder 1000 caracteres.');
        isValid = false;
    }
    
    // Validate priority
    const prioridad = parseInt($('#prioridad').val());
    if (!prioridad || prioridad < 1) {
        showFieldError('prioridad', 'La prioridad es requerida y debe ser mayor a 0.');
        isValid = false;
    } else if (prioridad > 999) {
        showFieldError('prioridad', 'La prioridad no puede ser mayor a 999.');
        isValid = false;
    }
    
    // Validate image (only required for creation)
    const action = $('#action').val();
    const imageFile = $('#imagen')[0].files[0];
    if (action === 'crear' && !imageFile) {
        showFieldError('imagen', 'La imagen es requerida.');
        isValid = false;
    }
    
    // Validate image file if provided
    if (imageFile) {
        // Check file size (8MB)
        const maxSize = 8 * 1024 * 1024;
        if (imageFile.size > maxSize) {
            showFieldError('imagen', 'La imagen es demasiado grande. Máximo 8MB permitido.');
            isValid = false;
        }
        
        // Check file type
        const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
        if (!allowedTypes.includes(imageFile.type)) {
            showFieldError('imagen', 'Tipo de archivo no permitido. Solo se permiten imágenes JPG, PNG y WebP.');
            isValid = false;
        }
    }
    
    return isValid;
}

/**
 * Show field validation error
 */
function showFieldError(fieldName, message) {
    const field = $('#' + fieldName);
    const errorDiv = $('#' + fieldName + '-error');
    
    field.addClass('is-invalid');
    errorDiv.text(message);
}

/**
 * Clear all validation states
 */
function clearValidationStates() {
    $('.form-control').removeClass('is-invalid');
    $('.invalid-feedback').text('');
}

/**
 * Reset form to initial state
 */
function resetForm() {
    $('#formValor')[0].reset();
    $('#valorId').val('');
    $('#action').val('crear');
    clearValidationStates();
    $('#currentImagePreview').hide();
    $('#newImagePreview').hide();
    $('#descripcion-count').text('0');
}

/**
 * Show loading state
 */
function showLoadingState(show) {
    if (show) {
        $('#loadingSpinner').removeClass('d-none');
        $('#btnSubmit').prop('disabled', true);
    } else {
        $('#loadingSpinner').addClass('d-none');
        $('#btnSubmit').prop('disabled', false);
    }
}

/**
 * Handle successful form submission
 */
function handleFormSuccess(response) {
    if (response.success) {
        showSweetAlertSuccess('¡Éxito!', response.message);
        $('#modalValor').modal('hide');
        // Reload page to refresh the table
        setTimeout(() => {
            location.reload();
        }, 1500);
    } else {
        showSweetAlertError('Error', response.message);
    }
}

/**
 * Handle form submission error
 */
function handleFormError(xhr, status, error) {
    let message = 'Error al procesar la solicitud.';
    
    if (xhr.responseJSON && xhr.responseJSON.message) {
        message = xhr.responseJSON.message;
    }
    
    showSweetAlertError('Error', message);
}

/**
 * Preview uploaded image
 */
function previewImage(input) {
    if (input.files && input.files[0]) {
        const reader = new FileReader();
        
        reader.onload = function(e) {
            $('#previewImage').attr('src', e.target.result);
            $('#newImagePreview').show();
        };
        
        reader.readAsDataURL(input.files[0]);
    } else {
        $('#newImagePreview').hide();
    }
}

/**
 * Show success alert
 */
function showSweetAlertSuccess(title, message) {
    Swal.fire({
        title: title,
        text: message,
        icon: 'success',
        confirmButtonColor: '#3085d6',
        background: '#2d353c',
        color: '#fff'
    });
}

/**
 * Show error alert
 */
function showSweetAlertError(title, message) {
    Swal.fire({
        title: title,
        text: message,
        icon: 'error',
        confirmButtonColor: '#3085d6',
        background: '#2d353c',
        color: '#fff'
    });
}
